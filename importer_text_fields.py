#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据导入工具 - 适用于text字段版本
已将问题字段(订单包装成本、货品原单价)修改为text类型，简化了数据处理逻辑
"""

import os
import pymysql
from openpyxl import load_workbook
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 数据库连接配置
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale',
    'charset': 'utf8mb4'
}

# 配置
DATA_FOLDER = 'data'  # Excel文件所在文件夹
TARGET_TABLE = 'wdt_saledetail'  # 目标数据库表
MAX_WORKERS = 3  # 最大并发线程数

# Excel文件的列名（92列）
EXCEL_COLUMNS = [
    '订单编号', '原始单号', '子单原始单号', '原始子订单号', '订单类型', '支付账号',
    '出库单编号', '仓库', '仓库类型', '店铺', '出库单状态', '出库状态', '分拣序号',
    '商家编码', '货品编号', '货品名称', '货品简称', '品牌', '分类', '规格码',
    '规格名称', '平台货品名称', '平台规格名称', '平台货品ID', '平台规格ID', '条形码',
    '货品数量', '货品原单价', '货品原总金额', '订单总优惠', '邮费', '货品成交价',
    '货品成交总价', '货品总优惠', '货到付款金额', '货品成本', '货品总成本', '固定成本',
    '固定总成本', '订单支付金额', '应收金额', '退款前支付金额', '单品支付金额', '分摊邮费',
    '预估邮资', '邮资成本', '订单包装成本', '订单毛利', '毛利率', '订单固定毛利',
    '固定毛利率', '客户网名', '收件人', '证件号码', '收货地区', '收货地址',
    '收件人手机', '收件人电话', '物流公司', '实际重量', '预估重量', '需开发票',
    '制单人', '打单员', '拣货员', '打包员', '检视员', '业务员', '验货员',
    '打印波次', '物流单打印状态', '发货单打印状态', '分拣单打印状态', '物流单号',
    '分拣单编号', '外部单号', '付款时间', '发货时间', '赠品方式', '买家留言',
    '客服备注', '打印备注', '备注', '包装', '来源组合装编码', '拆自组合装',
    '来源组合装数量', '体积', '分销商', '分销商编号'
]

# 数据库表的列名（93列，不包括自增主键id）
DB_COLUMNS = [
    '订单编号', '原始单号', '子单原始单号', '原始子订单号', '订单类型', '支付账号',
    '出库单编号', '仓库', '仓库类型', '店铺', '出库单状态', '出库状态', '分拣序号',
    '商家编码', '货品编号', '货品名称', '货品简称', '品牌', '分类', '规格码',
    '规格名称', '平台货品名称', '平台规格名称', '平台货品ID', '平台规格ID', '条形码',
    '货品数量', '货品原单价', '货品原总金额', '订单总优惠', '邮费', '货品成交价',
    '货品成交总价', '货品总优惠', '货到付款金额', '货品成本', '货品总成本', '固定成本',
    '固定总成本', '订单支付金额', '应收金额', '退款前支付金额', '单品支付金额', '分摊邮费',
    '预估邮资', '邮资成本', '订单包装成本', '订单毛利', '毛利率', '订单固定毛利',
    '固定毛利率', '客户网名', '收件人', '证件号码', '收货地区', '收货地址',
    '收件人手机', '收件人电话', '物流公司', '实际重量', '预估重量', '需开发票',
    '制单人', '打单员', '拣货员', '打包员', '检视员', '业务员', '验货员',
    '打印波次', '物流单打印状态', '发货单打印状态', '分拣单打印状态', '物流单号',
    '分拣单编号', '外部单号', '付款时间', '发货时间', '下单时间', '审核时间',
    '赠品方式', '买家留言', '客服备注', '打印备注', '备注', '包装',
    '来源组合装编码', '拆自组合装', '来源组合装数量', '体积', '分销商', '分销商编号',
    '分销原始单号'
]

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

def process_excel_file(file_path):
    """处理单个Excel文件"""
    try:
        print(f"[线程] 开始处理文件: {os.path.basename(file_path)}")

        # 创建数据库连接
        connection = get_db_connection()
        cursor = connection.cursor()

        # 加载Excel文件
        wb = load_workbook(file_path)
        ws = wb.active

        # 批量插入数据
        batch_data = []
        batch_size = 1000  # 每批次插入1000条数据

        for row in ws.iter_rows(min_row=2, values_only=True):
            if row and any(cell is not None for cell in row):  # 跳过空行
                # 将Excel行数据转换为数据库格式（添加缺失的列）
                db_row = convert_excel_row_to_db_row(row)
                batch_data.append(db_row)

                if len(batch_data) >= batch_size:
                    insert_batch_data(cursor, batch_data)
                    batch_data = []

        # 插入剩余数据
        if batch_data:
            insert_batch_data(cursor, batch_data)

        # 提交事务
        connection.commit()
        cursor.close()
        connection.close()

        print(f"[线程] ✅ 完成处理文件: {os.path.basename(file_path)}")
        return f"✅ 成功处理: {os.path.basename(file_path)}"

    except Exception as e:
        print(f"[线程] ❌ 处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
        return f"❌ 处理失败: {os.path.basename(file_path)} - {str(e)}"

def convert_excel_row_to_db_row(excel_row):
    """将Excel行数据转换为数据库行数据"""
    # 确保Excel行有92列
    excel_data = list(excel_row)
    while len(excel_data) < 92:
        excel_data.append(None)

    # 创建数据库行数据（93列）
    db_row = []

    # 定义需要特殊处理的日期时间字段
    datetime_fields = {'付款时间', '发货时间', '下单时间', '审核时间'}

    # 按照数据库列的顺序填充数据
    for db_col in DB_COLUMNS:
        if db_col in EXCEL_COLUMNS:
            # 找到Excel中对应列的索引
            excel_index = EXCEL_COLUMNS.index(db_col)
            value = excel_data[excel_index]

            # 对日期时间字段进行特殊处理
            if db_col in datetime_fields:
                # 如果是空字符串，转换为None（数据库NULL）
                if value == '' or value is None:
                    value = None
                # 如果是字符串但不是空字符串，保持原值（让数据库自己解析）
                # 如果是datetime对象，保持原值

            db_row.append(value)
        else:
            # 缺失的列填充None（对应数据库的NULL）
            db_row.append(None)

    return tuple(db_row)

def insert_batch_data(cursor, batch_data):
    """批量插入数据"""
    if not batch_data:
        return

    # 构建指定列名的INSERT语句
    column_names = ', '.join([f'`{col}`' for col in DB_COLUMNS])
    placeholders = ', '.join(['%s'] * len(DB_COLUMNS))
    sql = f"INSERT INTO {TARGET_TABLE} ({column_names}) VALUES ({placeholders})"

    try:
        cursor.executemany(sql, batch_data)
        print(f"成功批量插入 {len(batch_data)} 条数据")
    except Exception as e:
        print(f"批量插入数据时出错: {str(e)}")
        # 如果批量插入失败，尝试逐行插入
        for i, row in enumerate(batch_data):
            try:
                cursor.execute(sql, row)
                if (i + 1) % 100 == 0:
                    print(f"已逐行插入 {i + 1} 条数据")
            except Exception as row_error:
                print(f"插入行数据失败: {row} - {str(row_error)}")

def main():
    """主函数"""
    print("=== Excel数据导入工具 (Text字段版本) ===")
    print(f"数据文件夹: {DATA_FOLDER}")
    print(f"目标数据库: {DB_CONFIG['host']}/{DB_CONFIG['database']}")
    print(f"目标表: {TARGET_TABLE}")
    print(f"最大并发数: {MAX_WORKERS}")
    
    start_time = time.time()

    # 检查数据文件夹是否存在
    if not os.path.exists(DATA_FOLDER):
        print(f"❌ 数据文件夹 '{DATA_FOLDER}' 不存在")
        return

    # 获取所有Excel文件
    excel_files = [f for f in os.listdir(DATA_FOLDER) 
                   if f.endswith(('.xlsx', '.xls')) and not f.startswith('~')]

    if not excel_files:
        print(f"❌ 在文件夹 '{DATA_FOLDER}' 中没有找到Excel文件")
        return

    print(f"找到 {len(excel_files)} 个Excel文件")
    for i, file in enumerate(excel_files, 1):
        print(f"  {i}. {file}")

    # 构建完整文件路径
    file_paths = [os.path.join(DATA_FOLDER, file) for file in excel_files]

    # 使用线程池并行处理文件
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 提交所有任务
        future_to_file = {executor.submit(process_excel_file, file_path): file_path
                         for file_path in file_paths}

        # 收集结果
        results = []
        for future in as_completed(future_to_file):
            file_path = future_to_file[future]
            try:
                result = future.result()
                results.append(result)
                print(f"任务完成: {result}")
            except Exception as exc:
                error_msg = f"❌ 处理文件 {os.path.basename(file_path)} 时发生异常: {exc}"
                results.append(error_msg)
                print(error_msg)

    end_time = time.time()
    elapsed_time = end_time - start_time

    # 输出最终结果
    print("\n" + "="*60)
    print("📊 处理结果汇总:")
    print("="*60)
    
    success_count = sum(1 for r in results if r.startswith('✅'))
    failed_count = len(results) - success_count
    
    for result in results:
        print(f"  {result}")
    
    print(f"\n📈 统计信息:")
    print(f"  总文件数: {len(results)}")
    print(f"  成功: {success_count}")
    print(f"  失败: {failed_count}")
    print(f"  耗时: {elapsed_time:.2f} 秒")
    
    if failed_count == 0:
        print(f"\n🎉 所有文件处理成功！")
    else:
        print(f"\n⚠️  有 {failed_count} 个文件处理失败，请检查错误信息")

if __name__ == "__main__":
    main()
